const express = require('express');
const cors = require('cors');
const path = require('path');
const bodyParser = require('body-parser');
const api = require('./api');
const admin = require('./api/admin');
const priceAdmin = require('./api/price-admin');
const price = require('./api/price');
const locationVolumeRates = require('./api/location-volume-rates');
const exchangeRates = require('./api/exchange-rates');
const importExportPrices = require('./api/import-export-prices');
const setup = require('./api/setup');
const tpbank = require('./api/tpbank');
const warehouse = require('./api/warehouse');
const adminWarehouse = require('./api/admin-warehouse');
const invoice = require('./api/invoice');
const qrcode = require('./api/qrcode');
const purchaseOrder = require('./api/purchase-order');
const exportRoutes = require('./api/export');
const fs = require('fs'); // Vẫn có thể cần dùng fs cho việc khác
// const https = require('https'); // Không cần thiết cho việc tạo server nữa
const http = require('http'); // Sử dụng http để tạo server
const { createProxyMiddleware } = require('http-proxy-middleware');
const scheduledTasks = require(path.join(__dirname, 'scheduled-tasks')); // Import scheduled tasks
require('dotenv').config();

// Cấu hình SEO
const seoConfig = require('./seo-config');

const app = express();
const PORT = process.env.PORT || 3000; // Cổng nội bộ Node.js sẽ lắng nghe
const NODE_ENV = process.env.NODE_ENV || 'development';
const DOMAIN = process.env.DOMAIN || 'trustmove.vn';
const API_DOMAIN = process.env.API_DOMAIN || 'trustmove.vn';
// PROTOCOL phản ánh cách người dùng truy cập (qua Nginx), nên giữ là https
const PROTOCOL = 'https';
const ALLOWED_ORIGINS = process.env.ALLOWED_ORIGINS
    ? process.env.ALLOWED_ORIGINS.split(',')
    : [
        'http://trustmove.vn',
        'https://trustmove.vn',
        'http://www.trustmove.vn',
        'http://localhost',
        'http://localhost:3000',
        'https://www.trustmove.vn',
        // Thêm IP patterns cho TPBank proxy
        'https://**************',
        'http://**************'
      ];

// Không cần đường dẫn SSL ở đây nữa vì Nginx sẽ xử lý
// const SSL_KEY_PATH = process.env.SSL_KEY_PATH || '/etc/letsencrypt/live/trustmove.vn/privkey.pem';
// const SSL_CERT_PATH = process.env.SSL_CERT_PATH || '/etc/letsencrypt/live/trustmove.vn/fullchain.pem';

// Hiển thị thông tin cấu hình khi khởi động
console.log(`Môi trường: ${NODE_ENV}`);
console.log(`Domain người dùng truy cập (qua Nginx): ${DOMAIN}`);
console.log(`API Domain người dùng truy cập (qua Nginx): ${API_DOMAIN}`);
console.log(`Protocol người dùng truy cập (qua Nginx): ${PROTOCOL}`);
console.log(`Server sẽ chạy HTTP nội bộ trên cổng: ${PORT}`);

// Middleware
app.use(cors({
    origin: function(origin, callback) {
        const allowedDomains = ALLOWED_ORIGINS;
        if (!origin) return callback(null, true); // Cho phép nếu không có origin
        if (origin.includes(DOMAIN)) return callback(null, true); // Cho phép subdomain
        if (allowedDomains.indexOf(origin) !== -1) {
            return callback(null, true);
        }
        console.warn(`CORS không cho phép origin: ${origin}`);
        return callback(null, true); // Vẫn cho phép (ít nghiêm ngặt) - có thể đổi thành callback(new Error('Not allowed by CORS')) nếu muốn chặn hẳn
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true
}));

// Cấu hình static files và compression
if (NODE_ENV === 'production') {
    // Nén response (vẫn hữu ích ngay cả khi có Nginx)
    const compression = require('compression');
    app.use(compression());
    // Phục vụ static files từ thư mục public
    app.use(express.static(path.join(__dirname, 'public'), {
        maxAge: '7d', // Cache phía Node (ít quan trọng hơn cache Nginx)
        etag: true
    }));
    // Phục vụ các file tĩnh khác từ thư mục gốc
    app.use(express.static(path.join(__dirname), {
        maxAge: '7d',
        etag: true
    }));
} else {
    // Môi trường development không cache static
    // Phục vụ static files từ thư mục public trước
    app.use(express.static(path.join(__dirname, 'public')));
    // Phục vụ các file tĩnh khác từ thư mục gốc
    app.use(express.static(path.join(__dirname)));
}

// Cấu hình body-parser với error handling
app.use(bodyParser.json({
    limit: '50mb'
    // Loại bỏ verify function - empty body là hợp lệ
    // body-parser sẽ tự động parse empty body thành {}
}));

app.use(bodyParser.urlencoded({
    extended: true,
    limit: '50mb'
}));

// Middleware xử lý lỗi JSON parsing và body-parser
app.use((err, req, res, next) => {
    // Xử lý lỗi JSON syntax
    if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        console.error('Lỗi JSON parsing:', {
            message: err.message,
            url: req.url,
            method: req.method,
            contentType: req.headers['content-type'],
            contentLength: req.headers['content-length']
        });

        return res.status(400).json({
            error: 'Invalid JSON format',
            message: 'Request body contains malformed JSON',
            details: err.message
        });
    }

    // Xử lý lỗi body-parser khác (ví dụ: entity.verify.failed)
    if (err.type === 'entity.verify.failed') {
        console.error('Body verification failed:', {
            message: err.message,
            url: req.url,
            method: req.method,
            contentType: req.headers['content-type']
        });

        return res.status(400).json({
            error: 'Request body validation failed',
            message: 'The request body could not be processed',
            details: err.message
        });
    }

    next(err);
});

// Middleware để debug và phục vụ các yêu cầu truy cập file trong thư mục uploads/labels
app.use((req, res, next) => {
    if (req.url.includes('/uploads/labels')) {
        console.log('Đang truy cập file label:', req.url);
        const filePath = path.join(__dirname, 'public', req.url);
        console.log('Đường dẫn đầy đủ:', filePath);
        const fileExists = fs.existsSync(filePath);
        console.log('File tồn tại:', fileExists);

        // Nếu file tồn tại, phục vụ trực tiếp
        if (fileExists) {
            return res.sendFile(filePath);
        }
    }
    next();
});

// Cấu hình proxy cho Planka assets - PHẢI ĐẶT TRƯỚC CÁC STATIC MIDDLEWARE
const plankaAssetsProxy = createProxyMiddleware((pathname, req) => {
    // Proxy ALL /assets requests - không cần check referer vì có thể fonts không có referer
    return pathname.startsWith('/assets');
}, {
    target: 'http://localhost:1337',
    changeOrigin: true,
    logLevel: NODE_ENV === 'production' ? 'warn' : 'debug',
    headers: {
        'Origin': 'http://localhost:1337'  // Giả mạo origin để bypass CORS
    },
    onProxyReq: (proxyReq, req, res) => {
        // Override origin header để bypass CORS
        proxyReq.setHeader('Origin', 'http://localhost:1337');
        if (NODE_ENV !== 'production') {
            console.log(`Proxying Planka assets ${req.method} ${req.url} -> http://localhost:1337${req.url}`);
        }
    }
});

// Cấu hình proxy cho Planka - PHẢI ĐẶT TRƯỚC CÁC MIDDLEWARE KHÁC
const plankaProxy = createProxyMiddleware('/plan', {
    target: 'http://localhost:1337',
    changeOrigin: true,
    pathRewrite: {
        '^/plan': '', // Remove /plan prefix when forwarding to Planka
    },
    ws: true, // Support WebSocket for real-time features
    logLevel: NODE_ENV === 'production' ? 'warn' : 'debug',
    headers: {
        'Origin': 'http://localhost:1337'  // Giả mạo origin để bypass CORS
    },
    onError: (err, req, res) => {
        console.error('Lỗi proxy Planka:', err.message);
        res.status(503).json({
            error: 'Planka service không khả dụng',
            message: 'Vui lòng thử lại sau hoặc liên hệ admin'
        });
    },
    onProxyReq: (proxyReq, req, res) => {
        // Override origin header để bypass CORS
        proxyReq.setHeader('Origin', 'http://localhost:1337');
        if (NODE_ENV !== 'production') {
            console.log(`Proxying ${req.method} ${req.url} -> http://localhost:1337${req.url.replace('/plan', '')}`);
        }
    }
});

// Apply proxy middlewares - TRƯỚC STATIC và API routes
app.use(plankaAssetsProxy);  // Assets proxy phải đặt trước
app.use('/plan', plankaProxy);

// Middleware ghi log (giữ nguyên)
app.use((req, res, next) => {
    req.appConfig = {
        domain: DOMAIN,
        apiDomain: API_DOMAIN,
        protocol: PROTOCOL, // URL được tạo ra sẽ dùng https
        isProduction: NODE_ENV === 'production'
    };
    next();
});

// Sử dụng API routes (giữ nguyên)
app.use('/api', api);
app.use('/api/admin', admin);
app.use('/api/admin/price', priceAdmin);
app.use('/api/price', price);
app.use('/api/admin/price', locationVolumeRates);
app.use('/api/exchange-rates', exchangeRates);
app.use('/api/exchange-rates', importExportPrices);
app.use('/api/setup', setup);
app.use('/api/tpbank', tpbank);
app.use('/api/warehouse', warehouse);
app.use('/api/admin/warehouse', adminWarehouse);

app.use('/api/admin/invoices', invoice);
app.use('/api/invoices', invoice);
app.use('/api/qrcode', qrcode);
app.use('/api/admin/purchase-orders', purchaseOrder);
app.use('/api/purchase-orders', purchaseOrder);
app.use('/api/export', exportRoutes);

// Export system additional routes
const exportAuth = require('./api/export-auth');
const exportInvoices = require('./api/export-invoices');
const exportPdf = require('./api/export-pdf');
const cjLogistics = require('./api/cj-logistics');
const barcodeProcessing = require('./api/barcode-processing');

app.use('/api/export-auth', exportAuth);
app.use('/api/export/invoices', exportInvoices);
app.use('/api/export-pdf', exportPdf); // Standalone PDF endpoint
app.use('/api/cj-logistics', cjLogistics.router);
app.use('/api/barcode-processing', barcodeProcessing.router);

// Route cho robots.txt (giữ nguyên)
app.get('/robots.txt', (req, res) => {
    res.type('text/plain');
    res.send(seoConfig.generateRobotsTxt());
});

// Route cho sitemap.xml (giữ nguyên)
app.get('/sitemap.xml', (req, res) => {
    res.type('application/xml');
    res.send(seoConfig.generateSitemapXml());
});

// API để lấy cấu hình (giữ nguyên, dùng PROTOCOL=https)
app.get('/api/config', (req, res) => {
    const apiUrl = `${PROTOCOL}://${API_DOMAIN}`; // Tạo URL mà client sẽ thấy
    res.json({
        apiUrl: apiUrl,
        domain: DOMAIN,
        isProduction: NODE_ENV === 'production'
    });
});

// Cấu hình toàn cục cho trang (giữ nguyên, dùng PROTOCOL=https)
app.use((req, res, next) => {
    const apiUrl = `${PROTOCOL}://${API_DOMAIN}`; // Tạo URL mà client sẽ thấy
    res.locals.config = {
        apiUrl: apiUrl,
        domain: DOMAIN,
        isProduction: NODE_ENV === 'production'
    };
    next();
});

// Thêm header bảo mật (giữ nguyên)
if (NODE_ENV === 'production') {
    app.use((req, res, next) => {
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'SAMEORIGIN');
        res.setHeader('Referrer-Policy', 'same-origin');
        // Thêm CSP để cho phép load QR code từ sepay.vn
        res.setHeader('Content-Security-Policy',
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; " +
            "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; " +
            "img-src 'self' data: blob: https://qr.sepay.vn https://img.vietqr.io; " +
            "font-src 'self' https://cdnjs.cloudflare.com; " +
            "connect-src 'self';"
        );
        // Có thể thêm các header khác nếu Nginx chưa thêm
        next();
    });
} else {
    // Development mode - more relaxed CSP
    app.use((req, res, next) => {
        res.setHeader('Content-Security-Policy',
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; " +
            "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; " +
            "img-src 'self' data: blob: https://qr.sepay.vn https://img.vietqr.io; " +
            "font-src 'self' https://cdnjs.cloudflare.com; " +
            "connect-src 'self';"
        );
        next();
    });
}

// Proxy middleware đã được di chuyển lên trên (dòng 178)

// Phục vụ các trang HTML (giữ nguyên)
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin.html'));
});

// Serve export system page
app.get('/export', (req, res) => {
    res.sendFile(path.join(__dirname, 'export', 'index.html'));
});

// Serve export system static files
app.use('/export', express.static(path.join(__dirname, 'export')));
app.get('/price', (req, res) => {
    res.sendFile(path.join(__dirname, 'price.html'));
});
app.get('/insurance', (req, res) => {
    res.sendFile(path.join(__dirname, 'insurance.html'));
});
app.get('/about', (req, res) => {
    res.sendFile(path.join(__dirname, 'about.html'));
});
app.get('/docs', (req, res) => {
    res.sendFile(path.join(__dirname, 'docs.html'));
});
app.get('/scan', (req, res) => {
    res.sendFile(path.join(__dirname, 'warehouse-scan.html'));
});
app.get('/invoice', (req, res) => {
    res.sendFile(path.join(__dirname, 'invoice.html'));
});
app.get('/invoice-lookup', (req, res) => {
    res.sendFile(path.join(__dirname, 'invoice-lookup.html'));
});
app.get('/purchase-order', (req, res) => {
    res.sendFile(path.join(__dirname, 'purchase-order.html'));
});
app.get('/tracuudonhang', (req, res) => {
    res.sendFile(path.join(__dirname, 'purchase-order-lookup.html'));
});

// Xử lý lỗi 404 (giữ nguyên)
app.use((req, res) => {
    res.status(404).sendFile(path.join(__dirname, 'index.html')); // Hoặc trả về trang 404 tùy chỉnh
});

// Xử lý lỗi 500 (giữ nguyên)
app.use((err, req, res, next) => {
    console.error('Lỗi server:', err);
    res.status(500).sendFile(path.join(__dirname, 'index.html')); // Hoặc trả về trang lỗi 500
});

// --- KHỞI ĐỘNG SERVER HTTP ĐƠN GIẢN ---
// Nginx sẽ đảm nhận việc xử lý HTTPS và lắng nghe trên cổng 80/443 công cộng.
// Server Node.js này chỉ cần chạy HTTP trên cổng nội bộ PORT.
const server = http.createServer(app); // Sử dụng http thay vì https
server.listen(PORT, '0.0.0.0', () => { // Lắng nghe trên tất cả các interface
    console.log(`------------------------------------------------------------------`);
    console.log(`Server Node.js (HTTP) đang lắng nghe trên cổng nội bộ: ${PORT}`);
    console.log(`Chế độ môi trường: ${NODE_ENV}`);
    console.log(`Nginx được cấu hình để proxy tới cổng này.`);
    console.log(`Người dùng cuối sẽ truy cập qua Nginx tại: ${PROTOCOL}://${DOMAIN}`);
    console.log(`------------------------------------------------------------------`);

    // Khởi động các tác vụ định kỳ
    if (NODE_ENV === 'production') {
        scheduledTasks.startAllTasks();
        console.log('Đã khởi động các tác vụ định kỳ.');

        // Start export tracking updater
        const { startScheduledUpdater } = require('./jobs/export-tracking-updater');
        startScheduledUpdater();
        console.log('Đã khởi động export tracking updater.');
    } else {
        console.log('Chế độ phát triển: Các tác vụ định kỳ không được khởi động tự động.');
        console.log('Để khởi động tác vụ định kỳ, gọi: scheduledTasks.startAllTasks()');
    }
});
// --- ĐÃ XÓA LOGIC KHỞI ĐỘNG HTTPS VÀ HTTP REDIRECT CŨ ---


// Xử lý tín hiệu tắt
process.on('SIGTERM', () => {
    console.log('SIGTERM nhận được. Đang tắt server một cách an toàn.');

    // Dừng các tác vụ định kỳ
    scheduledTasks.stopAllTasks();
    console.log('Đã dừng các tác vụ định kỳ.');

    // Stop export tracking updater
    const { stopScheduledUpdater } = require('./jobs/export-tracking-updater');
    stopScheduledUpdater();
    console.log('Đã dừng export tracking updater.');

    server.close(() => { // Đảm bảo server đóng lại trước khi thoát
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT nhận được. Đang tắt server một cách an toàn.');

    // Dừng các tác vụ định kỳ
    scheduledTasks.stopAllTasks();
    console.log('Đã dừng các tác vụ định kỳ.');

    server.close(() => { // Đảm bảo server đóng lại trước khi thoát
        process.exit(0);
    });
});

module.exports = app; // Giữ lại nếu cần cho testing hoặc các mục đích khác