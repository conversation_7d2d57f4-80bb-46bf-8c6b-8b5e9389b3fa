const axios = require('axios');

async function testProxy() {
    console.log('🧪 Testing proxy connections...\n');
    
    try {
        // Test 1: Direct Planka connection
        console.log('1. Testing direct Planka (http://localhost:1337)...');
        const plankaResponse = await axios.get('http://localhost:1337/', {
            timeout: 5000,
            validateStatus: () => true // Accept any status
        });
        console.log(`✅ Direct Planka: Status ${plankaResponse.status}`);
        
        // Test 2: TrustMove main page
        console.log('\n2. Testing TrustMove (http://localhost:3000)...');
        const trustmoveResponse = await axios.get('http://localhost:3000/', {
            timeout: 5000,
            validateStatus: () => true
        });
        console.log(`✅ TrustMove: Status ${trustmoveResponse.status}`);
        
        // Test 3: Proxy connection
        console.log('\n3. Testing proxy (http://localhost:3000/plan)...');
        const proxyResponse = await axios.get('http://localhost:3000/plan', {
            timeout: 10000,
            validateStatus: () => true,
            maxRedirects: 5
        });
        console.log(`✅ Proxy: Status ${proxyResponse.status}`);
        console.log(`   Content-Type: ${proxyResponse.headers['content-type']}`);
        console.log(`   Content-Length: ${proxyResponse.headers['content-length'] || 'unknown'}`);
        
        if (proxyResponse.status === 200) {
            console.log('\n🎉 Proxy hoạt động thành công!');
        } else {
            console.log(`\n❌ Proxy có vấn đề - Status: ${proxyResponse.status}`);
        }
        
    } catch (error) {
        console.log(`\n❌ Lỗi: ${error.message}`);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Khắc phục:');
            console.log('1. Kiểm tra TrustMove server đã chạy: node server.js');
            console.log('2. Kiểm tra Planka server đã chạy: cd planka && npm start');
        } else if (error.code === 'ETIMEDOUT') {
            console.log('\n💡 Proxy bị timeout - có thể do configuration issue');
        }
    }
}

testProxy();