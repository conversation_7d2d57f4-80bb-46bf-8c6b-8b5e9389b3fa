const { spawn, exec } = require('child_process');
const path = require('path');
const util = require('util');
const execPromise = util.promisify(exec);

console.log('🚀 Khởi động TrustMove với Planka integration...\n');

// Hàm kiểm tra và giải phóng port
async function killProcessOnPort(port) {
    try {
        const { stdout } = await execPromise(`netstat -ano | findstr :${port}`);
        if (stdout.trim()) {
            const lines = stdout.trim().split('\n');
            for (const line of lines) {
                const parts = line.trim().split(/\s+/);
                const pid = parts[parts.length - 1];
                if (pid && pid !== '0') {
                    console.log(`⚠️  Port ${port} đang được sử dụng bởi PID ${pid}, đang dừng process...`);
                    await execPromise(`taskkill /F /PID ${pid}`);
                    console.log(`✅ Đã giải phóng port ${port}`);
                }
            }
        }
    } catch (error) {
        // Port không được sử dụng hoặc lỗi khác
        console.log(`✅ Port ${port} đã sẵn sàng`);
    }
}

async function startServices() {
    // Kiểm tra và giải phóng ports
    console.log('🔍 Kiểm tra ports...');
    await killProcessOnPort(3000);
    await killProcessOnPort(1337);
    
    console.log('\n📦 Khởi động TrustMove server (port 3000)...');
    const trustmoveProcess = spawn('node', ['server.js'], {
        cwd: __dirname,
        stdio: 'pipe'
    });

trustmoveProcess.stdout.on('data', (data) => {
    console.log(`[TrustMove] ${data.toString().trim()}`);
});

trustmoveProcess.stderr.on('data', (data) => {
    console.error(`[TrustMove Error] ${data.toString().trim()}`);
});

    // Đợi 3 giây rồi khởi động Planka
    setTimeout(() => {
        console.log('\n📋 Khởi động Planka server (port 1337)...');
        const plankaProcess = spawn('node', ['app.js'], {
            cwd: path.join(__dirname, 'planka'),
            stdio: 'pipe',
            env: {
                ...process.env,
                NODE_ENV: 'production',
                BASE_URL: 'http://localhost:1337',
                DATABASE_URL: 'postgresql://postgres:England1%40@localhost/planka',
                SECRET_KEY: 'spx_tracking_secret_key_2024',
                TZ: 'Asia/Ho_Chi_Minh'
            }
        });

    plankaProcess.stdout.on('data', (data) => {
        console.log(`[Planka] ${data.toString().trim()}`);
    });

    plankaProcess.stderr.on('data', (data) => {
        console.error(`[Planka Error] ${data.toString().trim()}`);
    });

        plankaProcess.on('close', (code) => {
            console.log(`[Planka] Process exited with code ${code}`);
        });

    }, 3000);

    // Đợi thêm 10 giây rồi chạy test
    setTimeout(() => {
        console.log('\n🧪 Chạy integration test...');
        const testProcess = spawn('node', ['test-plan-integration.js'], {
            cwd: __dirname,
            stdio: 'inherit'
        });
    }, 13000);

    // Xử lý tín hiệu tắt
    process.on('SIGINT', () => {
        console.log('\n⏹️  Đang dừng tất cả services...');
        trustmoveProcess.kill('SIGTERM');
        process.exit(0);
    });

    trustmoveProcess.on('close', (code) => {
        console.log(`[TrustMove] Process exited with code ${code}`);
    });

    console.log('\n✅ Hệ thống đã khởi động!');
    console.log('📌 Truy cập:');
    console.log('   - TrustMove: http://localhost:3000');
    console.log('   - Planka: http://localhost:3000/plan');
    console.log('\n⌨️  Nhấn Ctrl+C để dừng tất cả services');
}

// Khởi động services
startServices().catch(console.error);