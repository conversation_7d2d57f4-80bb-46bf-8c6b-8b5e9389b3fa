--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

-- Started on 2025-08-28 14:24:18

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 2 (class 3079 OID 33556)
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- TOC entry 5166 (class 0 OID 0)
-- Dependencies: 2
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- TOC entry 282 (class 1255 OID 33638)
-- Name: next_id(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.next_id(OUT id bigint) RETURNS bigint
    LANGUAGE plpgsql
    AS $$
      DECLARE
        shard INT := 1;
        epoch BIGINT := 1567191600000;
        sequence BIGINT;
        milliseconds BIGINT;
      BEGIN
        SELECT nextval('next_id_seq') % 1024 INTO sequence;
        SELECT FLOOR(EXTRACT(EPOCH FROM clock_timestamp()) * 1000) INTO milliseconds;
        id := (milliseconds - epoch) << 23;
        id := id | (shard << 10);
        id := id | (sequence);
      END;
    $$;


ALTER FUNCTION public.next_id(OUT id bigint) OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 248 (class 1259 OID 33899)
-- Name: action; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.action (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    user_id bigint,
    type text NOT NULL,
    data jsonb NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    board_id bigint
);


ALTER TABLE public.action OWNER TO postgres;

--
-- TOC entry 243 (class 1259 OID 33844)
-- Name: attachment; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.attachment (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    creator_user_id bigint,
    type text NOT NULL,
    data jsonb NOT NULL,
    name text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.attachment OWNER TO postgres;

--
-- TOC entry 230 (class 1259 OID 33711)
-- Name: background_image; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.background_image (
    id bigint DEFAULT public.next_id() NOT NULL,
    project_id bigint NOT NULL,
    dirname text NOT NULL,
    extension text NOT NULL,
    size_in_bytes bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.background_image OWNER TO postgres;

--
-- TOC entry 231 (class 1259 OID 33720)
-- Name: base_custom_field_group; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.base_custom_field_group (
    id bigint DEFAULT public.next_id() NOT NULL,
    project_id bigint NOT NULL,
    name text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.base_custom_field_group OWNER TO postgres;

--
-- TOC entry 232 (class 1259 OID 33729)
-- Name: board; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.board (
    id bigint DEFAULT public.next_id() NOT NULL,
    project_id bigint NOT NULL,
    "position" double precision NOT NULL,
    name text NOT NULL,
    default_view text NOT NULL,
    default_card_type text NOT NULL,
    limit_card_types_to_default_one boolean NOT NULL,
    always_display_card_creator boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.board OWNER TO postgres;

--
-- TOC entry 234 (class 1259 OID 33748)
-- Name: board_membership; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.board_membership (
    id bigint DEFAULT public.next_id() NOT NULL,
    project_id bigint NOT NULL,
    board_id bigint NOT NULL,
    user_id bigint NOT NULL,
    role text NOT NULL,
    can_comment boolean,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.board_membership OWNER TO postgres;

--
-- TOC entry 233 (class 1259 OID 33739)
-- Name: board_subscription; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.board_subscription (
    id bigint DEFAULT public.next_id() NOT NULL,
    board_id bigint NOT NULL,
    user_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.board_subscription OWNER TO postgres;

--
-- TOC entry 237 (class 1259 OID 33781)
-- Name: card; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.card (
    id bigint DEFAULT public.next_id() NOT NULL,
    board_id bigint NOT NULL,
    list_id bigint NOT NULL,
    creator_user_id bigint,
    prev_list_id bigint,
    cover_attachment_id bigint,
    type text NOT NULL,
    "position" double precision,
    name text NOT NULL,
    description text,
    due_date timestamp without time zone,
    stopwatch jsonb,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    list_changed_at timestamp without time zone,
    comments_total integer NOT NULL
);


ALTER TABLE public.card OWNER TO postgres;

--
-- TOC entry 240 (class 1259 OID 33814)
-- Name: card_label; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.card_label (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    label_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.card_label OWNER TO postgres;

--
-- TOC entry 239 (class 1259 OID 33805)
-- Name: card_membership; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.card_membership (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    user_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.card_membership OWNER TO postgres;

--
-- TOC entry 238 (class 1259 OID 33796)
-- Name: card_subscription; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.card_subscription (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    user_id bigint NOT NULL,
    is_permanent boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.card_subscription OWNER TO postgres;

--
-- TOC entry 247 (class 1259 OID 33889)
-- Name: comment; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.comment (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    user_id bigint,
    text text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.comment OWNER TO postgres;

--
-- TOC entry 245 (class 1259 OID 33866)
-- Name: custom_field; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.custom_field (
    id bigint DEFAULT public.next_id() NOT NULL,
    base_custom_field_group_id bigint,
    custom_field_group_id bigint,
    "position" double precision NOT NULL,
    name text NOT NULL,
    show_on_front_of_card boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.custom_field OWNER TO postgres;

--
-- TOC entry 244 (class 1259 OID 33854)
-- Name: custom_field_group; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.custom_field_group (
    id bigint DEFAULT public.next_id() NOT NULL,
    board_id bigint,
    card_id bigint,
    base_custom_field_group_id bigint,
    "position" double precision NOT NULL,
    name text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.custom_field_group OWNER TO postgres;

--
-- TOC entry 246 (class 1259 OID 33877)
-- Name: custom_field_value; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.custom_field_value (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    custom_field_group_id bigint NOT NULL,
    custom_field_id bigint NOT NULL,
    content text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.custom_field_value OWNER TO postgres;

--
-- TOC entry 223 (class 1259 OID 33639)
-- Name: file_reference; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.file_reference (
    id bigint DEFAULT public.next_id() NOT NULL,
    total integer,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.file_reference OWNER TO postgres;

--
-- TOC entry 225 (class 1259 OID 33661)
-- Name: identity_provider_user; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.identity_provider_user (
    id bigint DEFAULT public.next_id() NOT NULL,
    user_id bigint NOT NULL,
    issuer text NOT NULL,
    sub text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.identity_provider_user OWNER TO postgres;

--
-- TOC entry 235 (class 1259 OID 33760)
-- Name: label; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.label (
    id bigint DEFAULT public.next_id() NOT NULL,
    board_id bigint NOT NULL,
    "position" double precision NOT NULL,
    name text,
    color text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.label OWNER TO postgres;

--
-- TOC entry 236 (class 1259 OID 33770)
-- Name: list; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.list (
    id bigint DEFAULT public.next_id() NOT NULL,
    board_id bigint NOT NULL,
    type text NOT NULL,
    "position" double precision,
    name text,
    color text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.list OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 33543)
-- Name: migration; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.migration (
    id integer NOT NULL,
    name character varying(255),
    batch integer,
    migration_time timestamp with time zone
);


ALTER TABLE public.migration OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 33542)
-- Name: migration_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.migration_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.migration_id_seq OWNER TO postgres;

--
-- TOC entry 5167 (class 0 OID 0)
-- Dependencies: 218
-- Name: migration_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.migration_id_seq OWNED BY public.migration.id;


--
-- TOC entry 221 (class 1259 OID 33550)
-- Name: migration_lock; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.migration_lock (
    index integer NOT NULL,
    is_locked integer
);


ALTER TABLE public.migration_lock OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 33549)
-- Name: migration_lock_index_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.migration_lock_index_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.migration_lock_index_seq OWNER TO postgres;

--
-- TOC entry 5168 (class 0 OID 0)
-- Dependencies: 220
-- Name: migration_lock_index_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.migration_lock_index_seq OWNED BY public.migration_lock.index;


--
-- TOC entry 222 (class 1259 OID 33637)
-- Name: next_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.next_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.next_id_seq OWNER TO postgres;

--
-- TOC entry 249 (class 1259 OID 33909)
-- Name: notification; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notification (
    id bigint DEFAULT public.next_id() NOT NULL,
    user_id bigint NOT NULL,
    creator_user_id bigint,
    board_id bigint NOT NULL,
    card_id bigint NOT NULL,
    comment_id bigint,
    action_id bigint,
    type text NOT NULL,
    data jsonb NOT NULL,
    is_read boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.notification OWNER TO postgres;

--
-- TOC entry 250 (class 1259 OID 33923)
-- Name: notification_service; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notification_service (
    id bigint DEFAULT public.next_id() NOT NULL,
    user_id bigint,
    board_id bigint,
    url text NOT NULL,
    format text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.notification_service OWNER TO postgres;

--
-- TOC entry 227 (class 1259 OID 33684)
-- Name: project; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project (
    id bigint DEFAULT public.next_id() NOT NULL,
    owner_project_manager_id bigint,
    background_image_id bigint,
    name text NOT NULL,
    description text,
    background_type text,
    background_gradient text,
    is_hidden boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.project OWNER TO postgres;

--
-- TOC entry 228 (class 1259 OID 33693)
-- Name: project_favorite; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project_favorite (
    id bigint DEFAULT public.next_id() NOT NULL,
    project_id bigint NOT NULL,
    user_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.project_favorite OWNER TO postgres;

--
-- TOC entry 229 (class 1259 OID 33702)
-- Name: project_manager; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project_manager (
    id bigint DEFAULT public.next_id() NOT NULL,
    project_id bigint NOT NULL,
    user_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.project_manager OWNER TO postgres;

--
-- TOC entry 226 (class 1259 OID 33672)
-- Name: session; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.session (
    id bigint DEFAULT public.next_id() NOT NULL,
    user_id bigint NOT NULL,
    access_token text NOT NULL,
    http_only_token text,
    remote_address text NOT NULL,
    user_agent text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.session OWNER TO postgres;

--
-- TOC entry 242 (class 1259 OID 33833)
-- Name: task; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.task (
    id bigint DEFAULT public.next_id() NOT NULL,
    task_list_id bigint NOT NULL,
    assignee_user_id bigint,
    "position" double precision NOT NULL,
    name text NOT NULL,
    is_completed boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.task OWNER TO postgres;

--
-- TOC entry 241 (class 1259 OID 33823)
-- Name: task_list; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.task_list (
    id bigint DEFAULT public.next_id() NOT NULL,
    card_id bigint NOT NULL,
    "position" double precision NOT NULL,
    name text NOT NULL,
    show_on_front_of_card boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.task_list OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 33646)
-- Name: user_account; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_account (
    id bigint DEFAULT public.next_id() NOT NULL,
    email text NOT NULL,
    password text,
    role text NOT NULL,
    name text NOT NULL,
    username text,
    avatar jsonb,
    phone text,
    organization text,
    language text,
    subscribe_to_own_cards boolean NOT NULL,
    subscribe_to_card_when_commenting boolean NOT NULL,
    turn_off_recent_card_highlighting boolean NOT NULL,
    enable_favorites_by_default boolean NOT NULL,
    default_editor_mode text NOT NULL,
    default_home_view text NOT NULL,
    default_projects_order text NOT NULL,
    is_sso_user boolean NOT NULL,
    is_deactivated boolean NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    password_changed_at timestamp without time zone
);


ALTER TABLE public.user_account OWNER TO postgres;

--
-- TOC entry 4808 (class 2604 OID 33546)
-- Name: migration id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.migration ALTER COLUMN id SET DEFAULT nextval('public.migration_id_seq'::regclass);


--
-- TOC entry 4809 (class 2604 OID 33553)
-- Name: migration_lock index; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.migration_lock ALTER COLUMN index SET DEFAULT nextval('public.migration_lock_index_seq'::regclass);


--
-- TOC entry 5158 (class 0 OID 33899)
-- Dependencies: 248
-- Data for Name: action; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.action (id, card_id, user_id, type, data, created_at, updated_at, board_id) FROM stdin;
1586213284179084308	1586213284078421011	1586083064839144449	createCard	{"card": {"name": "Tìm tracking cigar"}, "list": {"id": "1586203211574805517", "name": "🆕 Mới", "type": "active"}}	2025-08-27 08:22:38.681	\N	1586177279375967239
***********69441817	1586213284078421011	1586083064839144449	moveCard	{"card": {"name": "Tìm tracking cigar 1Z65A5640371417984"}, "toList": {"id": "1586203283691668494", "name": "📋 Đã Phân Công", "type": "active"}, "fromList": {"id": "1586203211574805517", "name": "🆕 Mới", "type": "active"}}	2025-08-28 07:20:35.002	\N	1586177279375967239
\.


--
-- TOC entry 5153 (class 0 OID 33844)
-- Dependencies: 243
-- Data for Name: attachment; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.attachment (id, card_id, creator_user_id, type, data, name, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5140 (class 0 OID 33711)
-- Dependencies: 230
-- Data for Name: background_image; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.background_image (id, project_id, dirname, extension, size_in_bytes, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5141 (class 0 OID 33720)
-- Dependencies: 231
-- Data for Name: base_custom_field_group; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.base_custom_field_group (id, project_id, name, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5142 (class 0 OID 33729)
-- Dependencies: 232
-- Data for Name: board; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.board (id, project_id, "position", name, default_view, default_card_type, limit_card_types_to_default_one, always_display_card_creator, created_at, updated_at) FROM stdin;
1586177279375967239	1586175061268628485	65536	Cần xử lý	kanban	project	f	t	2025-08-27 07:11:06.572	2025-08-27 07:11:22.399
\.


--
-- TOC entry 5144 (class 0 OID 33748)
-- Dependencies: 234
-- Data for Name: board_membership; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.board_membership (id, project_id, board_id, user_id, role, can_comment, created_at, updated_at) FROM stdin;
1586177279409521672	1586175061268628485	1586177279375967239	1586083064839144449	editor	\N	2025-08-27 07:11:06.579	\N
\.


--
-- TOC entry 5143 (class 0 OID 33739)
-- Dependencies: 233
-- Data for Name: board_subscription; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.board_subscription (id, board_id, user_id, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5147 (class 0 OID 33781)
-- Dependencies: 237
-- Data for Name: card; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.card (id, board_id, list_id, creator_user_id, prev_list_id, cover_attachment_id, type, "position", name, description, due_date, stopwatch, created_at, updated_at, list_changed_at, comments_total) FROM stdin;
1586213284078421011	1586177279375967239	1586203283691668494	1586083064839144449	\N	\N	project	65536	Tìm tracking cigar 1Z65A5640371417984	ađaad	\N	\N	2025-08-27 08:22:38.667	2025-08-28 07:20:34.979	2025-08-28 07:20:34.97	0
\.


--
-- TOC entry 5150 (class 0 OID 33814)
-- Dependencies: 240
-- Data for Name: card_label; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.card_label (id, card_id, label_id, created_at, updated_at) FROM stdin;
1586218301682353174	1586213284078421011	1586218301405529109	2025-08-27 08:32:36.814	\N
\.


--
-- TOC entry 5149 (class 0 OID 33805)
-- Dependencies: 239
-- Data for Name: card_membership; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.card_membership (id, card_id, user_id, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5148 (class 0 OID 33796)
-- Dependencies: 238
-- Data for Name: card_subscription; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.card_subscription (id, card_id, user_id, is_permanent, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5157 (class 0 OID 33889)
-- Dependencies: 247
-- Data for Name: comment; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.comment (id, card_id, user_id, text, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5155 (class 0 OID 33866)
-- Dependencies: 245
-- Data for Name: custom_field; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.custom_field (id, base_custom_field_group_id, custom_field_group_id, "position", name, show_on_front_of_card, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5154 (class 0 OID 33854)
-- Dependencies: 244
-- Data for Name: custom_field_group; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.custom_field_group (id, board_id, card_id, base_custom_field_group_id, "position", name, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5156 (class 0 OID 33877)
-- Dependencies: 246
-- Data for Name: custom_field_value; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.custom_field_value (id, card_id, custom_field_group_id, custom_field_id, content, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5133 (class 0 OID 33639)
-- Dependencies: 223
-- Data for Name: file_reference; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.file_reference (id, total, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5135 (class 0 OID 33661)
-- Dependencies: 225
-- Data for Name: identity_provider_user; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.identity_provider_user (id, user_id, issuer, sub, created_at, updated_at) FROM stdin;
1586083065006916610	1586083064839144449	trustmove.vn	1	2025-08-27 04:03:55.346	\N
\.


--
-- TOC entry 5145 (class 0 OID 33760)
-- Dependencies: 235
-- Data for Name: label; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.label (id, board_id, "position", name, color, created_at, updated_at) FROM stdin;
1586218301405529109	1586177279375967239	65536	USA	summer-sky	2025-08-27 08:32:36.778	\N
\.


--
-- TOC entry 5146 (class 0 OID 33770)
-- Dependencies: 236
-- Data for Name: list; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.list (id, board_id, type, "position", name, color, created_at, updated_at) FROM stdin;
1586177279468241929	1586177279375967239	archive	\N	\N	\N	2025-08-27 07:11:06.584	\N
1586177279510184970	1586177279375967239	trash	\N	\N	\N	2025-08-27 07:11:06.584	\N
1586203211574805517	1586177279375967239	active	65536	🆕 Mới	\N	2025-08-27 08:02:37.91	2025-08-27 08:17:23.394
1586203283691668494	1586177279375967239	active	131072	📋 Đã Phân Công	lagoon-blue	2025-08-27 08:02:46.53	2025-08-27 08:17:40.714
1586211543056385039	1586177279375967239	active	196608	🔄 Đang Xử Lý	\N	2025-08-27 08:19:11.107	\N
1586212894930895888	1586177279375967239	active	262144	👀 Cần Review	\N	2025-08-27 08:21:52.277	2025-08-27 08:22:03.693
1586213069573325841	1586177279375967239	active	327680	✅ Hoàn Thành	\N	2025-08-27 08:22:13.096	\N
1586213144055776274	1586177279375967239	active	393216	❌ Hủy Bỏ	\N	2025-08-27 08:22:21.976	\N
\.


--
-- TOC entry 5129 (class 0 OID 33543)
-- Dependencies: 219
-- Data for Name: migration; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.migration (id, name, batch, migration_time) FROM stdin;
1	20250228000022_version_2.js	1	2025-08-27 09:09:48.327+07
2	20250522151122_add_board_activity_log.js	1	2025-08-27 09:09:48.334+07
3	20250523131647_add_comments_counter.js	1	2025-08-27 09:09:48.339+07
\.


--
-- TOC entry 5131 (class 0 OID 33550)
-- Dependencies: 221
-- Data for Name: migration_lock; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.migration_lock (index, is_locked) FROM stdin;
1	0
\.


--
-- TOC entry 5159 (class 0 OID 33909)
-- Dependencies: 249
-- Data for Name: notification; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notification (id, user_id, creator_user_id, board_id, card_id, comment_id, action_id, type, data, is_read, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5160 (class 0 OID 33923)
-- Dependencies: 250
-- Data for Name: notification_service; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notification_service (id, user_id, board_id, url, format, created_at, updated_at) FROM stdin;
1586192152134681611	\N	1586177279375967239	tgram://**********************************************/-1002671630308:2734	markdown	2025-08-27 07:40:39.543	\N
1586192307189711884	1586083064839144449	\N	tgram://**********************************************/-1002671630308:2734	markdown	2025-08-27 07:40:58.026	\N
\.


--
-- TOC entry 5137 (class 0 OID 33684)
-- Dependencies: 227
-- Data for Name: project; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project (id, owner_project_manager_id, background_image_id, name, description, background_type, background_gradient, is_hidden, created_at, updated_at) FROM stdin;
1586175061268628485	\N	\N	Tìm tracking	\N	\N	\N	f	2025-08-27 07:06:42.137	\N
\.


--
-- TOC entry 5138 (class 0 OID 33693)
-- Dependencies: 228
-- Data for Name: project_favorite; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project_favorite (id, project_id, user_id, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5139 (class 0 OID 33702)
-- Dependencies: 229
-- Data for Name: project_manager; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project_manager (id, project_id, user_id, created_at, updated_at) FROM stdin;
1586175061335737350	1586175061268628485	1586083064839144449	2025-08-27 07:06:42.162	\N
\.


--
-- TOC entry 5136 (class 0 OID 33672)
-- Dependencies: 226
-- Data for Name: session; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.session (id, user_id, access_token, http_only_token, remote_address, user_agent, created_at, updated_at, deleted_at) FROM stdin;
1586083065065636867	1586083064839144449	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjU2YmUzNzE1LTk2MjMtNGRhYy04MTE4LWFkZTczNGI3ZDc4YyJ9.eyJpYXQiOjE3NTYyNjc0MzUsImV4cCI6MTc4NzgwMzQzNSwic3ViIjoiMTU4NjA4MzA2NDgzOTE0NDQ0OSJ9.NeL728VSGVxeHK-ocg3Q9BytYK3ZjzNAtjYitdVaiaE	\N	::1	axios/1.11.0	2025-08-27 04:03:55.353	\N	\N
1586086527404868612	1586083064839144449	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImJkMjMzM2JjLWRhYjktNDdlNy1hYmU0LWFkODk2YzkzNzljNSJ9.eyJpYXQiOjE3NTYyNjc4NDgsImV4cCI6MTc4NzgwMzg0OCwic3ViIjoiMTU4NjA4MzA2NDgzOTE0NDQ0OSJ9.SC3m5M2AqCWqVD9ZU7kv02ZStp1Jq0b5avA8b8vE_GU	69c04dc2-b97b-4a82-a9cc-75bcd576c68f	::1	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36	2025-08-27 04:10:48.086	\N	\N
1586288327877723159	1586083064839144449	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImZiYTFiYTBlLTZjN2ItNDRiZC04YTRlLTU5YzNkN2ZkODRjNCJ9.eyJpYXQiOjE3NTYyOTE5MDQsImV4cCI6MTc4NzgyNzkwNCwic3ViIjoiMTU4NjA4MzA2NDgzOTE0NDQ0OSJ9.wzjLa7FQtoagyzleK3a1qRGXn3_pGpACmrcH7hx15-8	42713049-19d4-4733-a4d0-4609c22d3d4d	::1	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36	2025-08-27 10:51:44.543	2025-08-27 10:51:49.073	2025-08-27 10:51:49.066
1586344398424114200	1586083064839144449	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ijc5OWY5NDhiLTk2YTEtNGI2Mi04YWNmLTAzOWRlMGE5ZWRlOSJ9.eyJpYXQiOjE3NTYyOTg1ODgsImV4cCI6MTc4NzgzNDU4OCwic3ViIjoiMTU4NjA4MzA2NDgzOTE0NDQ0OSJ9.Aa2Th8hUtpCf_f3AAK4_YW0nYasfa4pZlqMOUuBTNes	c060a89a-f9eb-4384-8ed0-4ed6bff74ea8	::1	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36	2025-08-27 12:43:08.695	\N	\N
\.


--
-- TOC entry 5152 (class 0 OID 33833)
-- Dependencies: 242
-- Data for Name: task; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.task (id, task_list_id, assignee_user_id, "position", name, is_completed, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5151 (class 0 OID 33823)
-- Dependencies: 241
-- Data for Name: task_list; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.task_list (id, card_id, "position", name, show_on_front_of_card, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5134 (class 0 OID 33646)
-- Dependencies: 224
-- Data for Name: user_account; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_account (id, email, password, role, name, username, avatar, phone, organization, language, subscribe_to_own_cards, subscribe_to_card_when_commenting, turn_off_recent_card_highlighting, enable_favorites_by_default, default_editor_mode, default_home_view, default_projects_order, is_sso_user, is_deactivated, created_at, updated_at, password_changed_at) FROM stdin;
1586083064839144449	<EMAIL>	\N	admin	trustmove	trustmove	\N	\N	TrustMove	\N	f	t	f	f	wysiwyg	groupedProjects	byDefault	t	f	2025-08-27 04:03:55.309	\N	\N
\.


--
-- TOC entry 5169 (class 0 OID 0)
-- Dependencies: 218
-- Name: migration_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.migration_id_seq', 3, true);


--
-- TOC entry 5170 (class 0 OID 0)
-- Dependencies: 220
-- Name: migration_lock_index_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.migration_lock_index_seq', 1, true);


--
-- TOC entry 5171 (class 0 OID 0)
-- Dependencies: 222
-- Name: next_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.next_id_seq', 25, true);


--
-- TOC entry 4969 (class 2606 OID 33906)
-- Name: action action_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.action
    ADD CONSTRAINT action_pkey PRIMARY KEY (id);


--
-- TOC entry 4944 (class 2606 OID 33851)
-- Name: attachment attachment_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachment
    ADD CONSTRAINT attachment_pkey PRIMARY KEY (id);


--
-- TOC entry 4879 (class 2606 OID 33718)
-- Name: background_image background_image_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.background_image
    ADD CONSTRAINT background_image_pkey PRIMARY KEY (id);


--
-- TOC entry 4882 (class 2606 OID 33727)
-- Name: base_custom_field_group base_custom_field_group_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.base_custom_field_group
    ADD CONSTRAINT base_custom_field_group_pkey PRIMARY KEY (id);


--
-- TOC entry 4894 (class 2606 OID 33758)
-- Name: board_membership board_membership_board_id_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_membership
    ADD CONSTRAINT board_membership_board_id_user_id_unique UNIQUE (board_id, user_id);


--
-- TOC entry 4896 (class 2606 OID 33755)
-- Name: board_membership board_membership_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_membership
    ADD CONSTRAINT board_membership_pkey PRIMARY KEY (id);


--
-- TOC entry 4885 (class 2606 OID 33736)
-- Name: board board_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board
    ADD CONSTRAINT board_pkey PRIMARY KEY (id);


--
-- TOC entry 4889 (class 2606 OID 33746)
-- Name: board_subscription board_subscription_board_id_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_subscription
    ADD CONSTRAINT board_subscription_board_id_user_id_unique UNIQUE (board_id, user_id);


--
-- TOC entry 4891 (class 2606 OID 33744)
-- Name: board_subscription board_subscription_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.board_subscription
    ADD CONSTRAINT board_subscription_pkey PRIMARY KEY (id);


--
-- TOC entry 4928 (class 2606 OID 33821)
-- Name: card_label card_label_card_id_label_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.card_label
    ADD CONSTRAINT card_label_card_id_label_id_unique UNIQUE (card_id, label_id);


--
-- TOC entry 4931 (class 2606 OID 33819)
-- Name: card_label card_label_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.card_label
    ADD CONSTRAINT card_label_pkey PRIMARY KEY (id);


--
-- TOC entry 4923 (class 2606 OID 33812)
-- Name: card_membership card_membership_card_id_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.card_membership
    ADD CONSTRAINT card_membership_card_id_user_id_unique UNIQUE (card_id, user_id);


--
-- TOC entry 4925 (class 2606 OID 33810)
-- Name: card_membership card_membership_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.card_membership
    ADD CONSTRAINT card_membership_pkey PRIMARY KEY (id);


--
-- TOC entry 4915 (class 2606 OID 33788)
-- Name: card card_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.card
    ADD CONSTRAINT card_pkey PRIMARY KEY (id);


--
-- TOC entry 4918 (class 2606 OID 33803)
-- Name: card_subscription card_subscription_card_id_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.card_subscription
    ADD CONSTRAINT card_subscription_card_id_user_id_unique UNIQUE (card_id, user_id);


--
-- TOC entry 4920 (class 2606 OID 33801)
-- Name: card_subscription card_subscription_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.card_subscription
    ADD CONSTRAINT card_subscription_pkey PRIMARY KEY (id);


--
-- TOC entry 4964 (class 2606 OID 33896)
-- Name: comment comment_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comment
    ADD CONSTRAINT comment_pkey PRIMARY KEY (id);


--
-- TOC entry 4949 (class 2606 OID 33861)
-- Name: custom_field_group custom_field_group_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.custom_field_group
    ADD CONSTRAINT custom_field_group_pkey PRIMARY KEY (id);


--
-- TOC entry 4954 (class 2606 OID 33873)
-- Name: custom_field custom_field_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.custom_field
    ADD CONSTRAINT custom_field_pkey PRIMARY KEY (id);


--
-- TOC entry 4957 (class 2606 OID 33886)
-- Name: custom_field_value custom_field_value_card_id_custom_field_group_id_custom_field_i; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.custom_field_value
    ADD CONSTRAINT custom_field_value_card_id_custom_field_group_id_custom_field_i UNIQUE (card_id, custom_field_group_id, custom_field_id);


--
-- TOC entry 4961 (class 2606 OID 33884)
-- Name: custom_field_value custom_field_value_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.custom_field_value
    ADD CONSTRAINT custom_field_value_pkey PRIMARY KEY (id);


--
-- TOC entry 4843 (class 2606 OID 33644)
-- Name: file_reference file_reference_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.file_reference
    ADD CONSTRAINT file_reference_pkey PRIMARY KEY (id);


--
-- TOC entry 4855 (class 2606 OID 33670)
-- Name: identity_provider_user identity_provider_user_issuer_sub_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.identity_provider_user
    ADD CONSTRAINT identity_provider_user_issuer_sub_unique UNIQUE (issuer, sub);


--
-- TOC entry 4857 (class 2606 OID 33668)
-- Name: identity_provider_user identity_provider_user_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.identity_provider_user
    ADD CONSTRAINT identity_provider_user_pkey PRIMARY KEY (id);


--
-- TOC entry 4901 (class 2606 OID 33767)
-- Name: label label_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.label
    ADD CONSTRAINT label_pkey PRIMARY KEY (id);


--
-- TOC entry 4905 (class 2606 OID 33777)
-- Name: list list_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.list
    ADD CONSTRAINT list_pkey PRIMARY KEY (id);


--
-- TOC entry 4841 (class 2606 OID 33555)
-- Name: migration_lock migration_lock_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.migration_lock
    ADD CONSTRAINT migration_lock_pkey PRIMARY KEY (index);


--
-- TOC entry 4839 (class 2606 OID 33548)
-- Name: migration migration_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.migration
    ADD CONSTRAINT migration_pkey PRIMARY KEY (id);


--
-- TOC entry 4977 (class 2606 OID 33916)
-- Name: notification notification_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT notification_pkey PRIMARY KEY (id);


--
-- TOC entry 4981 (class 2606 OID 33930)
-- Name: notification_service notification_service_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification_service
    ADD CONSTRAINT notification_service_pkey PRIMARY KEY (id);


--
-- TOC entry 4869 (class 2606 OID 33698)
-- Name: project_favorite project_favorite_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_favorite
    ADD CONSTRAINT project_favorite_pkey PRIMARY KEY (id);


--
-- TOC entry 4871 (class 2606 OID 33700)
-- Name: project_favorite project_favorite_project_id_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_favorite
    ADD CONSTRAINT project_favorite_project_id_user_id_unique UNIQUE (project_id, user_id);


--
-- TOC entry 4874 (class 2606 OID 33707)
-- Name: project_manager project_manager_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_manager
    ADD CONSTRAINT project_manager_pkey PRIMARY KEY (id);


--
-- TOC entry 4876 (class 2606 OID 33709)
-- Name: project_manager project_manager_project_id_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project_manager
    ADD CONSTRAINT project_manager_project_id_user_id_unique UNIQUE (project_id, user_id);


--
-- TOC entry 4867 (class 2606 OID 33691)
-- Name: project project_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project
    ADD CONSTRAINT project_pkey PRIMARY KEY (id);


--
-- TOC entry 4860 (class 2606 OID 33682)
-- Name: session session_access_token_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.session
    ADD CONSTRAINT session_access_token_unique UNIQUE (access_token);


--
-- TOC entry 4862 (class 2606 OID 33679)
-- Name: session session_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.session
    ADD CONSTRAINT session_pkey PRIMARY KEY (id);


--
-- TOC entry 4934 (class 2606 OID 33830)
-- Name: task_list task_list_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.task_list
    ADD CONSTRAINT task_list_pkey PRIMARY KEY (id);


--
-- TOC entry 4938 (class 2606 OID 33840)
-- Name: task task_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.task
    ADD CONSTRAINT task_pkey PRIMARY KEY (id);


--
-- TOC entry 4846 (class 2606 OID 33655)
-- Name: user_account user_account_email_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_account
    ADD CONSTRAINT user_account_email_unique UNIQUE (email);


--
-- TOC entry 4849 (class 2606 OID 33653)
-- Name: user_account user_account_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_account
    ADD CONSTRAINT user_account_pkey PRIMARY KEY (id);


--
-- TOC entry 4853 (class 2606 OID 33660)
-- Name: user_account user_account_username_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_account
    ADD CONSTRAINT user_account_username_unique EXCLUDE USING btree (username WITH =) WHERE ((username IS NOT NULL));


--
-- TOC entry 4966 (class 1259 OID 33933)
-- Name: action_board_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX action_board_id_index ON public.action USING btree (board_id);


--
-- TOC entry 4967 (class 1259 OID 33907)
-- Name: action_card_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX action_card_id_index ON public.action USING btree (card_id);


--
-- TOC entry 4970 (class 1259 OID 33908)
-- Name: action_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX action_user_id_index ON public.action USING btree (user_id);


--
-- TOC entry 4941 (class 1259 OID 33852)
-- Name: attachment_card_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX attachment_card_id_index ON public.attachment USING btree (card_id);


--
-- TOC entry 4942 (class 1259 OID 33853)
-- Name: attachment_creator_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX attachment_creator_user_id_index ON public.attachment USING btree (creator_user_id);


--
-- TOC entry 4880 (class 1259 OID 33719)
-- Name: background_image_project_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX background_image_project_id_index ON public.background_image USING btree (project_id);


--
-- TOC entry 4883 (class 1259 OID 33728)
-- Name: base_custom_field_group_project_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX base_custom_field_group_project_id_index ON public.base_custom_field_group USING btree (project_id);


--
-- TOC entry 4897 (class 1259 OID 33756)
-- Name: board_membership_project_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX board_membership_project_id_index ON public.board_membership USING btree (project_id);


--
-- TOC entry 4898 (class 1259 OID 33759)
-- Name: board_membership_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX board_membership_user_id_index ON public.board_membership USING btree (user_id);


--
-- TOC entry 4886 (class 1259 OID 33738)
-- Name: board_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX board_position_index ON public.board USING btree ("position");


--
-- TOC entry 4887 (class 1259 OID 33737)
-- Name: board_project_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX board_project_id_index ON public.board USING btree (project_id);


--
-- TOC entry 4892 (class 1259 OID 33747)
-- Name: board_subscription_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX board_subscription_user_id_index ON public.board_subscription USING btree (user_id);


--
-- TOC entry 4908 (class 1259 OID 33789)
-- Name: card_board_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_board_id_index ON public.card USING btree (board_id);


--
-- TOC entry 4909 (class 1259 OID 33791)
-- Name: card_creator_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_creator_user_id_index ON public.card USING btree (creator_user_id);


--
-- TOC entry 4910 (class 1259 OID 33795)
-- Name: card_description_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_description_index ON public.card USING gin (description public.gin_trgm_ops);


--
-- TOC entry 4929 (class 1259 OID 33822)
-- Name: card_label_label_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_label_label_id_index ON public.card_label USING btree (label_id);


--
-- TOC entry 4911 (class 1259 OID 33793)
-- Name: card_list_changed_at_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_list_changed_at_index ON public.card USING btree (list_changed_at);


--
-- TOC entry 4912 (class 1259 OID 33790)
-- Name: card_list_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_list_id_index ON public.card USING btree (list_id);


--
-- TOC entry 4926 (class 1259 OID 33813)
-- Name: card_membership_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_membership_user_id_index ON public.card_membership USING btree (user_id);


--
-- TOC entry 4913 (class 1259 OID 33794)
-- Name: card_name_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_name_index ON public.card USING gin (name public.gin_trgm_ops);


--
-- TOC entry 4916 (class 1259 OID 33792)
-- Name: card_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_position_index ON public.card USING btree ("position");


--
-- TOC entry 4921 (class 1259 OID 33804)
-- Name: card_subscription_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX card_subscription_user_id_index ON public.card_subscription USING btree (user_id);


--
-- TOC entry 4962 (class 1259 OID 33897)
-- Name: comment_card_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX comment_card_id_index ON public.comment USING btree (card_id);


--
-- TOC entry 4965 (class 1259 OID 33898)
-- Name: comment_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX comment_user_id_index ON public.comment USING btree (user_id);


--
-- TOC entry 4951 (class 1259 OID 33874)
-- Name: custom_field_base_custom_field_group_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_base_custom_field_group_id_index ON public.custom_field USING btree (base_custom_field_group_id);


--
-- TOC entry 4952 (class 1259 OID 33875)
-- Name: custom_field_custom_field_group_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_custom_field_group_id_index ON public.custom_field USING btree (custom_field_group_id);


--
-- TOC entry 4945 (class 1259 OID 33864)
-- Name: custom_field_group_base_custom_field_group_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_group_base_custom_field_group_id_index ON public.custom_field_group USING btree (base_custom_field_group_id);


--
-- TOC entry 4946 (class 1259 OID 33862)
-- Name: custom_field_group_board_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_group_board_id_index ON public.custom_field_group USING btree (board_id);


--
-- TOC entry 4947 (class 1259 OID 33863)
-- Name: custom_field_group_card_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_group_card_id_index ON public.custom_field_group USING btree (card_id);


--
-- TOC entry 4950 (class 1259 OID 33865)
-- Name: custom_field_group_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_group_position_index ON public.custom_field_group USING btree ("position");


--
-- TOC entry 4955 (class 1259 OID 33876)
-- Name: custom_field_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_position_index ON public.custom_field USING btree ("position");


--
-- TOC entry 4958 (class 1259 OID 33887)
-- Name: custom_field_value_custom_field_group_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_value_custom_field_group_id_index ON public.custom_field_value USING btree (custom_field_group_id);


--
-- TOC entry 4959 (class 1259 OID 33888)
-- Name: custom_field_value_custom_field_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX custom_field_value_custom_field_id_index ON public.custom_field_value USING btree (custom_field_id);


--
-- TOC entry 4844 (class 1259 OID 33645)
-- Name: file_reference_total_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX file_reference_total_index ON public.file_reference USING btree (total);


--
-- TOC entry 4858 (class 1259 OID 33671)
-- Name: identity_provider_user_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX identity_provider_user_user_id_index ON public.identity_provider_user USING btree (user_id);


--
-- TOC entry 4899 (class 1259 OID 33768)
-- Name: label_board_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX label_board_id_index ON public.label USING btree (board_id);


--
-- TOC entry 4902 (class 1259 OID 33769)
-- Name: label_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX label_position_index ON public.label USING btree ("position");


--
-- TOC entry 4903 (class 1259 OID 33778)
-- Name: list_board_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX list_board_id_index ON public.list USING btree (board_id);


--
-- TOC entry 4906 (class 1259 OID 33780)
-- Name: list_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX list_position_index ON public.list USING btree ("position");


--
-- TOC entry 4907 (class 1259 OID 33779)
-- Name: list_type_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX list_type_index ON public.list USING btree (type);


--
-- TOC entry 4971 (class 1259 OID 33921)
-- Name: notification_action_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_action_id_index ON public.notification USING btree (action_id);


--
-- TOC entry 4972 (class 1259 OID 33919)
-- Name: notification_card_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_card_id_index ON public.notification USING btree (card_id);


--
-- TOC entry 4973 (class 1259 OID 33920)
-- Name: notification_comment_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_comment_id_index ON public.notification USING btree (comment_id);


--
-- TOC entry 4974 (class 1259 OID 33918)
-- Name: notification_creator_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_creator_user_id_index ON public.notification USING btree (creator_user_id);


--
-- TOC entry 4975 (class 1259 OID 33922)
-- Name: notification_is_read_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_is_read_index ON public.notification USING btree (is_read);


--
-- TOC entry 4979 (class 1259 OID 33932)
-- Name: notification_service_board_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_service_board_id_index ON public.notification_service USING btree (board_id);


--
-- TOC entry 4982 (class 1259 OID 33931)
-- Name: notification_service_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_service_user_id_index ON public.notification_service USING btree (user_id);


--
-- TOC entry 4978 (class 1259 OID 33917)
-- Name: notification_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX notification_user_id_index ON public.notification USING btree (user_id);


--
-- TOC entry 4872 (class 1259 OID 33701)
-- Name: project_favorite_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX project_favorite_user_id_index ON public.project_favorite USING btree (user_id);


--
-- TOC entry 4877 (class 1259 OID 33710)
-- Name: project_manager_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX project_manager_user_id_index ON public.project_manager USING btree (user_id);


--
-- TOC entry 4865 (class 1259 OID 33692)
-- Name: project_owner_project_manager_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX project_owner_project_manager_id_index ON public.project USING btree (owner_project_manager_id);


--
-- TOC entry 4863 (class 1259 OID 33683)
-- Name: session_remote_address_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX session_remote_address_index ON public.session USING btree (remote_address);


--
-- TOC entry 4864 (class 1259 OID 33680)
-- Name: session_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX session_user_id_index ON public.session USING btree (user_id);


--
-- TOC entry 4936 (class 1259 OID 33842)
-- Name: task_assignee_user_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX task_assignee_user_id_index ON public.task USING btree (assignee_user_id);


--
-- TOC entry 4932 (class 1259 OID 33831)
-- Name: task_list_card_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX task_list_card_id_index ON public.task_list USING btree (card_id);


--
-- TOC entry 4935 (class 1259 OID 33832)
-- Name: task_list_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX task_list_position_index ON public.task_list USING btree ("position");


--
-- TOC entry 4939 (class 1259 OID 33843)
-- Name: task_position_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX task_position_index ON public.task USING btree ("position");


--
-- TOC entry 4940 (class 1259 OID 33841)
-- Name: task_task_list_id_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX task_task_list_id_index ON public.task USING btree (task_list_id);


--
-- TOC entry 4847 (class 1259 OID 33658)
-- Name: user_account_is_deactivated_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX user_account_is_deactivated_index ON public.user_account USING btree (is_deactivated);


--
-- TOC entry 4850 (class 1259 OID 33656)
-- Name: user_account_role_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX user_account_role_index ON public.user_account USING btree (role);


--
-- TOC entry 4851 (class 1259 OID 33657)
-- Name: user_account_username_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX user_account_username_index ON public.user_account USING btree (username);


-- Completed on 2025-08-28 14:24:19

--
-- PostgreSQL database dump complete
--

