// ecosystem.config.js
module.exports = {
  apps : [{
    name   : 'trustmove',          // Tên ứng dụng trong PM2
    script : 'server.js',          // File để chạy
    cwd    : '/root/trustmove/',   // Th<PERSON> mục làm việc
    env_production: {              // Biến môi trường khi chạy với --env production
       NODE_ENV: 'production',
       PORT: 3000,                 // Chạy trên cổng 3000
       DOMAIN: 'trustmove.vn',
       API_DOMAIN: 'trustmove.vn',
       ENABLE_HTTPS: 'false',      // QUAN TRỌNG: Đặt là 'false' để không chiếm cổng 80
       // Thêm các biến môi trường khác từ file .env của bạn nếu cần
       // Ví dụ: ALLOWED_ORIGINS: 'http://trustmove.vn,https://trustmove.vn,...' 
    }
  }]
};